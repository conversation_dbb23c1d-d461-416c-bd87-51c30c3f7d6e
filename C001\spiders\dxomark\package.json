{"name": "dxomark", "type": "module", "version": "1.0.0", "main": "main.js", "scripts": {"start": "node main.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "axios": "^1.10.0", "cheerio": "^1.1.0", "puppeteer": "^24.7.1", "puppeteer-core": "^24.14.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "vm2": "^3.9.19", "winston": "^3.11.0"}, "keywords": ["dxomark", "ratings", "scraping", "data-harvester"], "author": "RD Data Harvester Team", "license": "ISC", "description": "DXOMARK ratings spider for RD Data Harvester"}